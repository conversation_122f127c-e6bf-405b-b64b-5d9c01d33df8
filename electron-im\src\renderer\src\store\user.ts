// 用户/在线列表状态管理
import { ref, computed } from 'vue'
import { apiClient, type User } from '../api'

// 用户状态
const currentUser = ref<User | null>(null)
const isAuthenticated = ref(false)

// 初始化用户状态
const initializeAuth = () => {
  const user = apiClient.getCurrentUser()
  const token = apiClient.getToken()

  if (user && token) {
    currentUser.value = user
    isAuthenticated.value = true
  }
}

// 登录
const login = async (username: string, password: string) => {
  try {
    const response = await apiClient.login({ username, password })

    if (response.success) {
      currentUser.value = response.user
      isAuthenticated.value = true
      return { success: true, message: '登录成功' }
    } else {
      return { success: false, message: '登录失败' }
    }
  } catch (error) {
    console.error('登录失败:', error)
    throw error
  }
}

// 登出
const logout = () => {
  apiClient.logout()
  currentUser.value = null
  isAuthenticated.value = false
}

// 计算属性
const userDisplayName = computed(() => {
  return currentUser.value?.displayName || currentUser.value?.username || ''
})

const userAvatar = computed(() => {
  return currentUser.value?.avatar || '/avatars/default.png'
})

// 导出用户状态和方法
export const useUserStore = () => {
  return {
    // 状态
    currentUser,
    isAuthenticated,

    // 计算属性
    userDisplayName,
    userAvatar,

    // 方法
    initializeAuth,
    login,
    logout
  }
}
