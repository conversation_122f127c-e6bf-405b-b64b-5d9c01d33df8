import { resolve } from 'path'
import { defineConfig, externalizeDepsPlugin } from 'electron-vite'
import vue from '@vitejs/plugin-vue'
import tailwindcss from '@tailwindcss/vite'

export default defineConfig({
  main: {
    plugins: [externalizeDepsPlugin()]
  },
  preload: {
    plugins: [
      externalizeDepsPlugin({
        exclude: ['@electron-toolkit/preload']
      })
    ]
  },
  renderer: {
    base: '/zhuyuqian',
    resolve: {
      alias: {
        '@renderer': resolve('src/renderer/src')
        // '@': resolve('src/renderer/src')
      }
    },
    plugins: [vue(), tailwindcss()],
    server: {
      host: '0.0.0.0',
      port: 3000,
      proxy: {
        '/zhuyuqian/api': {
          target: 'http://***********:3000',
          changeOrigin: true,
          secure: false,
          rewrite: (path) => path.replace(/^\/zhuyuqian\/api/, '/api')
        },
        '/zhuyuqian/ws': {
          target: 'ws://***********:3000',
          changeOrigin: true,
          ws: true,
          rewrite: (path) => path.replace(/^\/zhuyuqian\/ws/, '/ws')
        }
      }
    },
    build: {
      rollupOptions: {
        input: {
          main: resolve('src/renderer/index.html')
        }
      }
    }
  }
})
