// 消息状态管理 - 重构后的主Store
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { apiClient } from '../../api'
import { useWebSocketStore } from '../websocket'

// 导入类型定义
import type { Message, ChatSession } from './types'

// 导入各个模块
import { MessageHandler } from './message'
import { SessionManager } from './session'
import { CacheManager } from './cache'
import { WebSocketIntegration } from './websocket-integration'

export const useMessageStore = defineStore('message', () => {
  // 获取全局 WebSocket 状态
  const webSocketStore = useWebSocketStore()

  // 核心状态
  const messages = ref<Map<string, Message[]>>(new Map()) // 按用户ID分组的消息
  const chatSessions = ref<Map<string, ChatSession>>(new Map()) // 聊天会话
  const currentChatUserId = ref<string>('') // 当前聊天的用户ID
  const isLoading = ref(false)
  const error = ref<string>('')

  // 初始化各个管理模块
  const messageHandler = new MessageHandler(messages, currentChatUserId, error)
  const sessionManager = new SessionManager(chatSessions, currentChatUserId, messages)
  const cacheManager = new CacheManager(messages, isLoading, error)
  const webSocketIntegration = new WebSocketIntegration(error)

  // 计算属性
  const currentMessages = computed(() => {
    if (!currentChatUserId.value) return []
    return messages.value.get(currentChatUserId.value) || []
  })

  // 从SessionManager获取计算属性
  const sortedChatSessions = sessionManager.sortedChatSessions
  const totalUnreadCount = sessionManager.totalUnreadCount

  // 获取当前用户ID的辅助函数
  const getCurrentUserId = (): string => {
    const user = apiClient.getCurrentUser()
    return user?.id || ''
  }

  // 初始化WebSocket连接
  const initWebSocket = async (token?: string) => {
    await webSocketIntegration.initWebSocket(
      token,
      () => cacheManager.initDatabase(getCurrentUserId),
      (message) =>
        messageHandler.handleIncomingMessage(message, getCurrentUserId, (userId, msg) =>
          sessionManager.updateChatSession(userId, msg, getCurrentUserId)
        ),
      (message) => messageHandler.handleIncomingSystemMessage(message)
    )
  }

  // 发送消息
  const sendMessage = async (receiverId: string, content: string): Promise<boolean> => {
    return await messageHandler.sendMessage(receiverId, content, getCurrentUserId, (userId, msg) =>
      sessionManager.updateChatSession(userId, msg, getCurrentUserId)
    )
  }

  // 获取聊天历史
  const loadChatHistory = async (otherUserId: string, page = 1, limit = 50): Promise<boolean> => {
    return await cacheManager.loadChatHistory(
      otherUserId,
      page,
      limit,
      (userId, msg) => sessionManager.updateChatSession(userId, msg, getCurrentUserId),
      (userId) => sessionManager.createEmptySession(userId)
    )
  }

  // 设置当前聊天用户
  const setCurrentChatUser = (userId: string) => {
    sessionManager.setCurrentChatUser(userId, getCurrentUserId)
  }

  // 断开WebSocket连接
  const disconnectWebSocket = () => {
    webSocketIntegration.disconnectWebSocket()
  }

  // 手动重连WebSocket
  const manualReconnectWebSocket = () => {
    webSocketIntegration.manualReconnectWebSocket()
  }

  // 清除错误
  const clearError = () => {
    error.value = ''
    webSocketIntegration.clearError()
  }

  // 批量设置聊天会话（用于从API加载的联系人数据）
  const setChatSessions = (sessions: ChatSession[]) => {
    sessionManager.setChatSessions(sessions)
  }

  // 设置联系人信息（从登录后获取的联系人列表中设置）
  const setContactsInfo = (contacts: Array<{ id: string; user: any }>) => {
    sessionManager.setContactsInfo(contacts)
  }

  // 重试发送失败的消息
  const retryMessage = async (userId: string, messageId: string): Promise<boolean> => {
    return await messageHandler.retryMessage(userId, messageId)
  }

  // 获取连接状态信息
  const getConnectionInfo = () => {
    return webSocketIntegration.getConnectionInfo()
  }

  // 清理资源
  const cleanup = () => {
    webSocketIntegration.cleanup()
    cacheManager.clearCache()
    sessionManager.clearAllSessions()
  }

  return {
    // 状态
    messages,
    chatSessions,
    currentChatUserId,
    wsState: webSocketStore.state, // 使用全局 WebSocket 状态
    isLoading,
    error,

    // 计算属性
    currentMessages,
    sortedChatSessions,
    totalUnreadCount,

    // 核心方法
    initWebSocket,
    sendMessage,
    loadChatHistory,
    setCurrentChatUser,
    disconnectWebSocket,
    manualReconnectWebSocket,
    clearError,
    retryMessage,
    setChatSessions,
    setContactsInfo,

    // 辅助方法
    getConnectionInfo,
    cleanup,

    // 暴露 WebSocket store 的方法
    webSocketStore,

    // 暴露各个管理模块（用于高级用法或测试）
    messageHandler,
    sessionManager,
    cacheManager,
    webSocketIntegration
  }
})

// 重新导出类型，方便外部使用
export type { Message, ChatSession, ChatHistoryResponse } from './types'
