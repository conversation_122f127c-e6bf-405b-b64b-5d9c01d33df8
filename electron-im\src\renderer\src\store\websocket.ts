// WebSocket 全局状态管理
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { WebSocketState, wsService } from '../services/websocketService'

export const useWebSocketStore = defineStore('websocket', () => {
  // 状态
  const state = ref<WebSocketState>(WebSocketState.CONNECTING)
  const reconnectAttempts = ref(0)
  const hasEverReconnected = ref(false)
  const isManuallyHidden = ref(false)
  const lastStateChangeTime = ref<Date>(new Date())

  // 计算属性
  const isConnected = computed(() => state.value === WebSocketState.CONNECTED)
  const isConnecting = computed(() => state.value === WebSocketState.CONNECTING)
  const isReconnecting = computed(() => state.value === WebSocketState.RECONNECTING)
  const isDisconnected = computed(() => state.value === WebSocketState.DISCONNECTED)
  const isError = computed(() => state.value === WebSocketState.ERROR)

  // 状态文本
  const statusText = computed(() => {
    switch (state.value) {
      case WebSocketState.CONNECTING:
        return hasEverReconnected.value || reconnectAttempts.value > 0
          ? '正在重新连接...'
          : '正在连接...'
      case WebSocketState.CONNECTED:
        // 检查连接健康状态
        try {
          const health = wsService?.getConnectionHealth?.()
          if (health) {
            if (health.status === 'warning') {
              return '连接不稳定，正在检测...'
            } else if (health.status === 'unhealthy') {
              return '连接异常，即将重连...'
            }
          }
        } catch (error) {
          // 忽略错误，使用默认文本
        }

        return hasEverReconnected.value || reconnectAttempts.value > 0
          ? '已恢复实时连接'
          : '连接成功'
      case WebSocketState.DISCONNECTED:
        return hasEverReconnected.value || reconnectAttempts.value > 0
          ? '连接已断开，正在尝试重新连接...'
          : '连接已断开'
      case WebSocketState.RECONNECTING:
        return `正在尝试第 ${reconnectAttempts.value} 次重连...`
      case WebSocketState.ERROR:
        return reconnectAttempts.value >= 5 ? `连接失败，已尝试 5 次重连` : '连接错误'
      default:
        return '未知状态'
    }
  })

  // 状态样式类
  const statusClasses = computed(() => {
    switch (state.value) {
      case WebSocketState.CONNECTING:
      case WebSocketState.RECONNECTING:
        return 'bg-blue-50 text-blue-700'
      case WebSocketState.CONNECTED:
        return 'bg-green-50 text-green-700'
      case WebSocketState.DISCONNECTED:
        return reconnectAttempts.value > 0
          ? 'bg-yellow-50 text-yellow-700'
          : 'bg-gray-50 text-gray-700'
      case WebSocketState.ERROR:
        return 'bg-red-50 text-red-700'
      default:
        return 'bg-gray-50 text-gray-700'
    }
  })

  // 是否应该显示状态
  const shouldShowStatus = computed(() => {
    // 如果手动隐藏了，只在非连接状态时显示
    if (isManuallyHidden.value) {
      return !isConnected.value
    }

    // 连接中和重连中状态总是显示
    if (isConnecting.value || isReconnecting.value || isError.value || isDisconnected.value) {
      return true
    }

    // 连接成功状态也显示（用户可以手动关闭）
    if (isConnected.value) {
      return true
    }

    return false
  })

  // 是否可以关闭状态提示
  const canClose = computed(() => {
    // 只有在连接成功且没有手动隐藏时才显示关闭按钮
    // 添加延迟显示逻辑：连接成功后1-2秒才显示关闭按钮
    if (!isConnected.value || isManuallyHidden.value) {
      return false
    }

    // 检查连接成功的时间，如果刚连接成功，延迟显示关闭按钮
    const timeSinceLastChange = Date.now() - lastStateChangeTime.value.getTime()
    const delayMs = hasEverReconnected.value ? 2000 : 1000 // 重连成功2秒，首次连接1秒

    return timeSinceLastChange > delayMs
  })

  // Actions
  const setState = (newState: WebSocketState) => {
    if (state.value !== newState) {
      const oldState = state.value
      state.value = newState
      lastStateChangeTime.value = new Date()

      console.log('WebSocketStore - 状态变更:', {
        from: oldState,
        to: newState,
        timestamp: lastStateChangeTime.value.toISOString()
      })

      // 如果状态变为连接成功，设置定时器来触发 canClose 的重新计算
      if (newState === WebSocketState.CONNECTED) {
        const delayMs = hasEverReconnected.value ? 2000 : 1000
        setTimeout(() => {
          // 触发响应式更新，通过修改时间戳来强制重新计算 canClose
          lastStateChangeTime.value = new Date(lastStateChangeTime.value.getTime())
          console.log('WebSocketStore - 触发 canClose 重新计算')
        }, delayMs + 100) // 稍微延迟一点确保计算正确
      }
    }
  }

  const setReconnectAttempts = (attempts: number) => {
    if (reconnectAttempts.value !== attempts) {
      console.log('WebSocketStore - 重连次数变更:', {
        from: reconnectAttempts.value,
        to: attempts
      })
      reconnectAttempts.value = attempts
    }
  }

  const setHasEverReconnected = (value: boolean) => {
    if (hasEverReconnected.value !== value) {
      console.log('WebSocketStore - hasEverReconnected 变更:', {
        from: hasEverReconnected.value,
        to: value
      })
      hasEverReconnected.value = value
    }
  }

  const setManuallyHidden = (hidden: boolean) => {
    console.log('WebSocketStore - 手动隐藏状态变更:', {
      from: isManuallyHidden.value,
      to: hidden
    })
    isManuallyHidden.value = hidden
  }

  // 重置所有状态
  const reset = () => {
    console.log('WebSocketStore - 重置所有状态')
    state.value = WebSocketState.CONNECTING
    reconnectAttempts.value = 0
    hasEverReconnected.value = false
    isManuallyHidden.value = false
    lastStateChangeTime.value = new Date()
  }

  // 同步 WebSocket 服务状态
  const syncFromWebSocketService = (wsService: any) => {
    const wsState = wsService.getState()
    const wsReconnectAttempts = wsService.getReconnectAttempts()
    const wsHasEverReconnected = wsService.getHasEverReconnected()

    console.log('WebSocketStore - 开始同步状态:', {
      currentStoreState: state.value,
      newWsState: wsState,
      currentStoreReconnectAttempts: reconnectAttempts.value,
      newWsReconnectAttempts: wsReconnectAttempts,
      currentStoreHasEverReconnected: hasEverReconnected.value,
      newWsHasEverReconnected: wsHasEverReconnected
    })

    setState(wsState)
    setReconnectAttempts(wsReconnectAttempts)
    setHasEverReconnected(wsHasEverReconnected)

    console.log('WebSocketStore - 同步状态完成:', {
      finalStoreState: state.value,
      finalStoreReconnectAttempts: reconnectAttempts.value,
      finalStoreHasEverReconnected: hasEverReconnected.value
    })
  }

  return {
    // 状态
    state,
    reconnectAttempts,
    hasEverReconnected,
    isManuallyHidden,
    lastStateChangeTime,

    // 计算属性
    isConnected,
    isConnecting,
    isReconnecting,
    isDisconnected,
    isError,
    statusText,
    statusClasses,
    shouldShowStatus,
    canClose,

    // Actions
    setState,
    setReconnectAttempts,
    setHasEverReconnected,
    setManuallyHidden,
    reset,
    syncFromWebSocketService
  }
})
