// 消息相关类型定义

// 消息接口定义
export interface Message {
  id: string
  senderId: string
  receiverId: string
  content: string
  timestamp: number
  type: number
  isRead?: boolean
  isSending?: boolean
  sendError?: string
}

// 聊天会话接口
export interface ChatSession {
  userId: string
  userName: string
  userAvatar: string
  lastMessage?: Message
  unreadCount: number
  lastActiveTime: number
}

// 聊天历史响应接口
export interface ChatHistoryResponse {
  success: boolean
  messages: Message[]
  otherUser: {
    id: string
    username: string
    displayName: string
    avatar?: string
  }
  pagination: {
    page: number
    limit: number
    hasMore: boolean
  }
}

// 消息状态更新接口
export interface MessageStatusUpdate {
  isSending?: boolean
  sendError?: string
  isRead?: boolean
}

// 数据库查询选项
export interface DatabaseQueryOptions {
  limit?: number
  offset?: number
}

// 用户详情响应接口
export interface UserDetailResponse {
  success: boolean
  user: {
    id: string
    username: string
    displayName: string
    avatar?: string
  }
  message?: string
}
