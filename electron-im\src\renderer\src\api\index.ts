// API 请求服务
import { API_CONFIG, APP_CONFIG } from '../config'

// 用户信息接口
export interface User {
  id: string
  username: string
  email: string
  displayName: string
  avatar: string
  isOnline: boolean
  lastOnlineTime: string
}

// 登录请求接口
export interface LoginRequest {
  username: string
  password: string
}

// 登录响应接口
export interface LoginResponse {
  success: boolean
  token: string
  user: User
}

// 获取用户列表响应接口 - 现在支持包含lastMessage的用户数据
export interface UsersResponse {
  success: boolean
  users: (User & { lastMessage?: UserLastMessage | null })[]
}

// 用户最后消息接口
export interface UserLastMessage {
  senderName: string
  timestamp: string
  content: string
}

// 包含最后消息的用户接口 - 直接扩展User接口以支持新的API响应格式
export interface UserWithLastMessage extends User {
  lastMessage?: UserLastMessage | null
}

// 聊天联系人接口（包含最后消息）
export interface ChatContact {
  user: User
  lastMessage?: {
    id: string
    content: string
    timestamp: string
    senderId: string
    receiverId: string
  }
  unreadCount: number
  lastActiveTime: string
}

// 获取聊天联系人列表响应接口
export interface ChatContactsResponse {
  success: boolean
  contacts: ChatContact[]
}

// 获取用户详情响应接口
export interface UserDetailResponse {
  success: boolean
  user: User
}

// 消息接口
export interface Message {
  id: string
  senderId: string
  receiverId: string
  content: string
  timestamp: number | string // 兼容API返回的字符串格式
  type: number
}

// 聊天历史响应接口
export interface ChatHistoryResponse {
  success: boolean
  messages: Message[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

// API 错误接口
export interface ApiError {
  success: false
  message: string
  code?: string
}

// HTTP 客户端类
class ApiClient {
  private baseURL: string
  private timeout: number

  constructor() {
    this.baseURL = API_CONFIG.BASE_URL
    this.timeout = API_CONFIG.TIMEOUT
  }

  // 通用请求方法
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseURL}${endpoint}`
    const token = localStorage.getItem(APP_CONFIG.TOKEN_KEY)

    const config: RequestInit = {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers
      }
    }

    // 设置超时
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), this.timeout)
    config.signal = controller.signal

    try {
      const response = await fetch(url, config)
      clearTimeout(timeoutId)

      if (!response.ok) {
        const errorText = await response.text()
        console.error(`API请求失败: ${response.status} ${response.statusText}`, errorText)

        // 根据不同的HTTP状态码提供更友好的错误信息
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`

        switch (response.status) {
          case 200:
            errorMessage = 'OK - 请求成功'
            break
          case 400:
            errorMessage = 'Bad Request - 请求参数错误'
            break
          case 401:
            errorMessage = 'Unauthorized - 未授权，需要登录'
            break
          case 403:
            errorMessage = 'Forbidden - 禁止访问'
            break
          case 404:
            errorMessage = 'Not Found - 资源不存在'
            break
          case 429:
            errorMessage = 'Too Many Requests - 请求太频繁，请稍后再试'
            break
          case 500:
            errorMessage = 'Internal Server Error - 服务器内部错误'
            break
          default:
            errorMessage = `HTTP ${response.status}: ${response.statusText}`
        }

        throw new Error(errorMessage)
      }

      const data = await response.json()
      return data
    } catch (error) {
      clearTimeout(timeoutId)
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('请求超时')
        }
        if (error.message.includes('Failed to fetch')) {
          throw new Error('网络连接失败，请检查网络设置或服务器状态')
        }
        if (error.message.includes('Mixed Content')) {
          throw new Error('安全策略限制，请使用HTTPS连接')
        }
        throw error
      }
      throw new Error('网络请求失败')
    }
  }

  // 登录方法
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      let response: LoginResponse

      if (API_CONFIG.USE_MOCK) {
        // 使用模拟登录
        console.log('mock登录方法')
      } else {
        // 使用真实API
        response = await this.request<LoginResponse>(API_CONFIG.ENDPOINTS.LOGIN, {
          method: 'POST',
          body: JSON.stringify(credentials)
        })
      }

      if (response.success && response.token) {
        // 保存 token 和用户信息
        localStorage.setItem(APP_CONFIG.TOKEN_KEY, response.token)
        localStorage.setItem(APP_CONFIG.USER_KEY, JSON.stringify(response.user))
      }

      return response
    } catch (error) {
      console.error('登录请求失败:', error)
      throw error
    }
  }

  // 获取用户列表方法
  async getUsers(): Promise<UsersResponse> {
    try {
      let response: UsersResponse

      if (API_CONFIG.USE_MOCK) {
        // 使用模拟数据
        console.log('mock获取用户列表方法')
      } else {
        // 使用真实API
        response = await this.request<UsersResponse>(API_CONFIG.ENDPOINTS.USERS, {
          method: 'GET'
        })
      }

      return response
    } catch (error) {
      console.error('获取用户列表失败:', error)
      throw error
    }
  }

  // 获取聊天联系人列表方法
  async getChatContacts(): Promise<ChatContactsResponse> {
    try {
      let response: ChatContactsResponse

      if (API_CONFIG.USE_MOCK) {
        // 使用模拟数据
        console.log('mock获取聊天联系人列表方法')
      } else {
        // 使用真实API
        response = await this.request<ChatContactsResponse>('/api/chat/contacts', {
          method: 'GET'
        })
      }

      return response
    } catch (error) {
      console.error('获取聊天联系人列表失败:', error)
      throw error
    }
  }

  // 获取用户详情方法
  async getUserDetail(userId: string): Promise<UserDetailResponse> {
    try {
      // 使用真实API
      const response = await this.request<UserDetailResponse>(`/api/users/${userId}`, {
        method: 'GET'
      })

      return response
    } catch (error) {
      console.error('获取用户详情失败:', error)
      throw error
    }
  }

  // 登出方法
  logout(): void {
    localStorage.removeItem(APP_CONFIG.TOKEN_KEY)
    localStorage.removeItem(APP_CONFIG.USER_KEY)
  }

  // 获取当前用户信息
  getCurrentUser(): User | null {
    const userStr = localStorage.getItem(APP_CONFIG.USER_KEY)
    if (userStr) {
      try {
        return JSON.parse(userStr)
      } catch {
        return null
      }
    }
    return null
  }

  // 检查是否已登录
  isAuthenticated(): boolean {
    return !!localStorage.getItem(APP_CONFIG.TOKEN_KEY)
  }

  // 获取 token
  getToken(): string | null {
    return localStorage.getItem(APP_CONFIG.TOKEN_KEY)
  }

  // 获取聊天历史
  async getChatHistory(otherUserId: string, page = 1, limit = 50): Promise<ChatHistoryResponse> {
    try {
      let response: ChatHistoryResponse

      if (API_CONFIG.USE_MOCK) {
        // 使用模拟数据
        console.log('mock获取历史记录')
      } else {
        // 使用真实API
        const apiUrl = `/api/messages/history/${otherUserId}?page=${page}&limit=${limit}`
        console.log(`请求聊天历史API: ${apiUrl}`)
        response = await this.request<ChatHistoryResponse>(apiUrl, { method: 'GET' })
        console.log(`聊天历史API响应 (用户${otherUserId}):`, response)
      }

      return response
    } catch (error) {
      console.error(`获取聊天历史失败 - 用户${otherUserId}:`, error)
      console.error('错误详情:', error.message || error)
      throw error
    }
  }
}

// 导出 API 客户端实例
export const apiClient = new ApiClient()
