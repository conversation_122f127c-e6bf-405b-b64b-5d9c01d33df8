// 消息处理模块
import type { Ref } from 'vue'
import { wsService } from '../../services/websocketService'
import type { TextMessage, SystemMessage } from '../../services/websocketService'
import { dbManager } from '../../db/db-manager'
import type { Message, MessageStatusUpdate } from './types'

export class MessageHandler {
  private messages: Ref<Map<string, Message[]>>
  private currentChatUserId: Ref<string>
  private error: Ref<string>

  constructor(
    messages: Ref<Map<string, Message[]>>,
    currentChatUserId: Ref<string>,
    error: Ref<string>
  ) {
    this.messages = messages
    this.currentChatUserId = currentChatUserId
    this.error = error
  }

  // 处理接收到的消息
  async handleIncomingMessage(
    textMessage: TextMessage,
    getCurrentUserId: () => string,
    updateChatSession: (userId: string, message: Message) => Promise<void>
  ) {
    console.log('🔍 [MessageHandler] 开始处理接收到的消息:', textMessage)

    const message: Message = {
      id: textMessage.id,
      senderId: textMessage.senderId,
      receiverId: textMessage.receiverId,
      content: textMessage.content,
      timestamp: textMessage.timestamp,
      type: 1, // TEXT_MESSAGE
      isRead: false
    }

    const currentUserId = getCurrentUserId()

    // 计算聊天用户ID
    const chatUserId = message.senderId === currentUserId ? message.receiverId : message.senderId

    // 如果是自己发送的消息，检查是否有对应的临时消息需要替换
    if (message.senderId === currentUserId) {
      const userMessages = this.messages.value.get(chatUserId) || []

      const tempMessageIndex = userMessages.findIndex(
        (msg) =>
          msg.id.startsWith('temp_') && // 查找临时消息
          msg.content === message.content &&
          msg.receiverId === message.receiverId &&
          msg.senderId === message.senderId
      )

      if (tempMessageIndex !== -1) {
        // 替换临时消息
        const finalMessage = {
          ...message,
          isSending: false
        }
        userMessages[tempMessageIndex] = finalMessage
        this.messages.value.set(chatUserId, userMessages)

        // 存储到数据库
        try {
          await dbManager.storeMessage(finalMessage, chatUserId)
          console.log(`🔍 [handleIncomingMessage] 替换的消息已存储到数据库: ${finalMessage.id}`)
        } catch (error) {
          console.error(
            `🔍 [handleIncomingMessage] 替换的消息存储到数据库失败: ${finalMessage.id}`,
            error
          )
        }

        // 更新聊天会话
        await updateChatSession(chatUserId, finalMessage)
        console.log('🔍 [handleIncomingMessage] 临时消息替换完成，直接返回')
        return
      } else {
        console.log('🔍 [handleIncomingMessage] ❌ 没有找到对应的临时消息，将作为新消息添加')
      }
    }

    // 添加消息到对应的聊天（如果不是替换临时消息的情况）
    await this.addMessageToChat(chatUserId, message)

    // 更新聊天会话
    await updateChatSession(chatUserId, message)

    console.log('🔍 [MessageHandler] 消息处理完成:', message)
  }

  // 处理接收到的系统消息
  handleIncomingSystemMessage(systemMessage: SystemMessage) {
    console.log('收到系统消息:', systemMessage)
    // 可以在这里添加系统消息的处理逻辑，比如显示通知等
    // 暂时只记录日志
  }

  // 发送消息
  async sendMessage(
    receiverId: string,
    content: string,
    getCurrentUserId: () => string,
    updateChatSession: (userId: string, message: Message) => Promise<void>
  ): Promise<boolean> {
    if (!content.trim()) {
      this.error.value = '消息内容不能为空'
      return false
    }

    if (!wsService.isConnected()) {
      this.error.value = 'WebSocket未连接'
      return false
    }

    // 创建临时消息对象
    const tempMessage: Message = {
      id: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      senderId: getCurrentUserId(),
      receiverId,
      content: content.trim(),
      timestamp: Date.now(),
      type: 1,
      isSending: true
    }

    // 立即添加到界面显示
    await this.addMessageToChat(receiverId, tempMessage)
    await updateChatSession(receiverId, tempMessage)

    try {
      // 发送消息
      const success = wsService.sendTextMessage(receiverId, content.trim())

      if (success) {
        // 更新消息状态为已发送
        this.updateMessageStatus(receiverId, tempMessage.id, { isSending: false })
        return true
      } else {
        // 发送失败
        this.updateMessageStatus(receiverId, tempMessage.id, {
          isSending: false,
          sendError: '发送失败'
        })
        this.error.value = '消息发送失败'
        return false
      }
    } catch (err) {
      console.error('发送消息失败:', err)
      this.updateMessageStatus(receiverId, tempMessage.id, {
        isSending: false,
        sendError: '发送失败'
      })
      this.error.value = err instanceof Error ? err.message : '发送失败'
      return false
    }
  }

  // 添加消息到聊天（去重）
  async addMessageToChat(userId: string, message: Message) {
    const userMessages = this.messages.value.get(userId) || []

    // 检查消息是否已存在，避免重复添加
    const existingMessage = userMessages.find((msg) => msg.id === message.id)
    if (existingMessage) {
      console.log('🔍 [addMessageToChat] ❌ 消息已存在，跳过添加:', message.id)
      return
    }

    userMessages.push(message)
    this.messages.value.set(userId, userMessages)

    // 如果不是临时消息，则存储到数据库
    if (!message.id.startsWith('temp_') && !message.isSending) {
      try {
        await dbManager.storeMessage(message, userId)
        console.log(`🔍 [addMessageToChat] 消息已存储到数据库: ${message.id}`)
      } catch (error) {
        console.error(`🔍 [addMessageToChat] 消息存储到数据库失败: ${message.id}`, error)
      }
    }
  }

  // 更新消息状态
  updateMessageStatus(userId: string, messageId: string, updates: MessageStatusUpdate) {
    const userMessages = this.messages.value.get(userId)
    if (userMessages) {
      const messageIndex = userMessages.findIndex((msg) => msg.id === messageId)
      if (messageIndex !== -1) {
        Object.assign(userMessages[messageIndex], updates)
      }
    }
  }

  // 重试发送失败的消息
  async retryMessage(userId: string, messageId: string): Promise<boolean> {
    const userMessages = this.messages.value.get(userId)
    if (!userMessages) return false

    const message = userMessages.find((msg) => msg.id === messageId)
    if (!message || !message.sendError) return false

    // 清除错误状态，设置为发送中
    this.updateMessageStatus(userId, messageId, {
      sendError: undefined,
      isSending: true
    })

    // 重新发送
    const success = wsService.sendTextMessage(message.receiverId, message.content)

    if (success) {
      this.updateMessageStatus(userId, messageId, { isSending: false })
      return true
    } else {
      this.updateMessageStatus(userId, messageId, {
        isSending: false,
        sendError: '重发失败'
      })
      return false
    }
  }
}
