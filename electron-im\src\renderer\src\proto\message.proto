syntax = "proto3";

package simpleim;

// 消息类型
enum MessageType {
  HEARTBEAT = 0;
  TEXT_MESSAGE = 1;
  ERROR = 100;
}

// 心跳消息（可扩展）
message Heartbeat {
  int64 timestamp = 1;   // 客户端发起心跳的时间戳（毫秒）
  string status = 2;    // 状态，当前暂定为 "online"
}



// 错误消息
message ErrorMessage {
  string code = 1;
  string message = 2;
}

// 主消息包装器
message IMMessage {
  int32 type = 1;           // 消息类型
  string messageId = 2;      // 消息ID
  int64 timestamp = 3;      // 时间戳
  string token = 4;         // 认证令牌
  TextMessage textMessage = 5; // 文本消息内容
}

// 统一消息（支持文本和表情混合）
message TextMessage {
  string receiverId = 1;    // 接收者ID
  string content = 2;       // 消息内容
}
