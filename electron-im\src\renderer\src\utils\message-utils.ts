import { simpleim } from '../proto/message.js'

// 导入生成的类型
type MessageType = simpleim.MessageType
type IIMMessage = simpleim.IIMMessage
type IHeartbeat = simpleim.IHeartbeat
type ITextMessage = simpleim.ITextMessage
type IErrorMessage = simpleim.IErrorMessage

/**
 * 消息工具类
 * 提供便捷的消息创建、序列化和反序列化方法
 */
export class MessageUtils {
  /**
   * 创建心跳消息
   */
  static createHeartbeat(status: string = 'online'): IIMMessage {
    const heartbeat: IHeartbeat = {
      timestamp: Date.now(),
      status
    }

    return {
      type: simpleim.MessageType.HEARTBEAT,
      heartbeat
    }
  }

  /**
   * 创建文本消息
   */
  static createTextMessage(
    senderId: string,
    receiverId: string,
    content: string,
    id?: string
  ): IIMMessage {
    const textMessage: ITextMessage = {
      id: id || this.generateMessageId(),
      senderId,
      receiverId,
      content,
      timestamp: Date.now()
    }

    return {
      type: simpleim.MessageType.TEXT_MESSAGE,
      textMessage
    }
  }

  /**
   * 创建错误消息
   */
  static createErrorMessage(code: string, message: string): IIMMessage {
    const errorMessage: IErrorMessage = {
      code,
      message
    }

    return {
      type: simpleim.MessageType.ERROR,
      errorMessage
    }
  }

  /**
   * 序列化消息为二进制数据
   */
  static serialize(message: IIMMessage): Uint8Array {
    try {
      return simpleim.IMMessage.encode(message).finish()
    } catch (error) {
      throw new Error(`Failed to serialize message: ${error}`)
    }
  }

  /**
   * 从二进制数据反序列化消息
   */
  static deserialize(data: Uint8Array | ArrayBuffer): simpleim.IMMessage {
    try {
      const uint8Array = data instanceof ArrayBuffer ? new Uint8Array(data) : data
      return simpleim.IMMessage.decode(uint8Array)
    } catch (error) {
      throw new Error(`Failed to deserialize message: ${error}`)
    }
  }

  /**
   * 验证消息格式
   */
  static validate(message: any): string | null {
    try {
      return simpleim.IMMessage.verify(message)
    } catch (error) {
      return `Validation error: ${error}`
    }
  }

  /**
   * 将消息转换为普通对象
   */
  static toObject(message: simpleim.IMMessage): any {
    return simpleim.IMMessage.toObject(message, {
      longs: String,
      enums: String,
      bytes: String
    })
  }

  /**
   * 从普通对象创建消息
   */
  static fromObject(obj: any): simpleim.IMMessage {
    return simpleim.IMMessage.fromObject(obj)
  }

  /**
   * 获取消息类型名称
   */
  static getMessageTypeName(type: MessageType): string {
    switch (type) {
      case simpleim.MessageType.HEARTBEAT:
        return 'HEARTBEAT'
      case simpleim.MessageType.TEXT_MESSAGE:
        return 'TEXT_MESSAGE'
      case simpleim.MessageType.ERROR:
        return 'ERROR'
      default:
        return 'UNKNOWN'
    }
  }

  /**
   * 检查消息是否为心跳消息
   */
  static isHeartbeat(message: simpleim.IMMessage): boolean {
    return message.type === simpleim.MessageType.HEARTBEAT
  }

  /**
   * 检查消息是否为文本消息
   */
  static isTextMessage(message: simpleim.IMMessage): boolean {
    return message.type === simpleim.MessageType.TEXT_MESSAGE
  }

  /**
   * 检查消息是否为错误消息
   */
  static isErrorMessage(message: simpleim.IMMessage): boolean {
    return message.type === simpleim.MessageType.ERROR
  }

  /**
   * 提取消息内容
   */
  static extractContent(message: simpleim.IMMessage): any {
    switch (message.type) {
      case simpleim.MessageType.HEARTBEAT:
        return message.heartbeat
      case simpleim.MessageType.TEXT_MESSAGE:
        return message.textMessage
      case simpleim.MessageType.ERROR:
        return message.errorMessage
      default:
        return null
    }
  }

  /**
   * 生成唯一消息ID
   */
  static generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 创建带token的消息
   */
  static addToken(message: IIMMessage, token: string): IIMMessage {
    return {
      ...message,
      token,
      timestamp: message.timestamp || Date.now(),
      messageId: message.messageId || this.generateMessageId()
    }
  }

  /**
   * 克隆消息
   */
  static clone(message: simpleim.IMMessage): simpleim.IMMessage {
    const obj = this.toObject(message)
    return this.fromObject(obj)
  }

  /**
   * 比较两个消息是否相等
   */
  static equals(msg1: simpleim.IMMessage, msg2: simpleim.IMMessage): boolean {
    try {
      const obj1 = this.toObject(msg1)
      const obj2 = this.toObject(msg2)
      return JSON.stringify(obj1) === JSON.stringify(obj2)
    } catch {
      return false
    }
  }

  /**
   * 获取消息大小（字节）
   */
  static getSize(message: IIMMessage): number {
    try {
      const buffer = this.serialize(message)
      return buffer.length
    } catch {
      return 0
    }
  }

  /**
   * 压缩消息（移除空字段）
   */
  static compress(message: IIMMessage): IIMMessage {
    const compressed: IIMMessage = {
      type: message.type
    }

    if (message.messageId) compressed.messageId = message.messageId
    if (message.timestamp) compressed.timestamp = message.timestamp
    if (message.token) compressed.token = message.token
    if (message.heartbeat) compressed.heartbeat = message.heartbeat
    if (message.textMessage) compressed.textMessage = message.textMessage
    if (message.errorMessage) compressed.errorMessage = message.errorMessage

    return compressed
  }
}
