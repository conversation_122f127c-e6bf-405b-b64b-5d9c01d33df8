<script setup lang="ts">
import { computed, onMounted } from 'vue'
import Login from './views/Login.vue'
import Chat from './views/Chat.vue'
import { useUserStore } from './store/user'

// 用户状态管理
const userStore = useUserStore()

// 计算当前应该显示的组件
const currentView = computed(() => {
  return userStore.isAuthenticated.value ? 'Chat' : 'Login'
})

// 组件挂载时初始化用户状态
onMounted(() => {
  userStore.initializeAuth()
})

// 登录成功后的处理逻辑
const handleLoginSuccess = () => {
  console.log('登录成功，切换到聊天页面')
}
</script>

<template>
  <div class="h-screen overflow-hidden">
    <Login v-if="currentView === 'Login'" @login-success="handleLoginSuccess" />
    <Chat v-else-if="currentView === 'Chat'" />
  </div>
</template>
