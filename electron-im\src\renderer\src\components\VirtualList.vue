<!-- 虚拟滚动列表组件 -->
<template>
  <div
    ref="containerRef"
    class="virtual-list-container"
    :style="{ height: containerHeight + 'px' }"
    @scroll="handleScroll"
  >
    <!-- 总高度占位符 -->
    <div :style="{ height: totalHeight + 'px', position: 'relative' }">
      <!-- 可视区域内的项目 -->
      <div
        :style="{
          transform: `translateY(${offsetY}px)`,
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0
        }"
      >
        <div
          v-for="item in visibleItems"
          :key="getItemKey(item.data)"
          :style="{ height: itemHeight + 'px' }"
          class="virtual-list-item"
        >
          <slot :item="item.data" :index="item.index"></slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" generic="T">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'

interface Props {
  // 数据列表
  items: T[]
  // 每项的高度（像素）
  itemHeight: number
  // 容器高度（像素）
  containerHeight: number
  // 缓冲区大小（在可视区域外渲染的项目数量）
  bufferSize?: number
  // 获取项目唯一键的函数
  getItemKey?: (item: T) => string | number
}

const props = withDefaults(defineProps<Props>(), {
  bufferSize: 5,
  getItemKey: (item: T, index?: number) => {
    // 默认使用索引作为key，如果item有id属性则使用id
    if (item && typeof item === 'object' && 'id' in item) {
      return (item as any).id
    }
    return index ?? Math.random()
  }
})

// 容器引用
const containerRef = ref<HTMLElement>()

// 滚动位置
const scrollTop = ref(0)

// 计算属性
const totalHeight = computed(() => props.items.length * props.itemHeight)

const visibleCount = computed(() => Math.ceil(props.containerHeight / props.itemHeight))

const startIndex = computed(() => {
  const index = Math.floor(scrollTop.value / props.itemHeight)
  return Math.max(0, index - props.bufferSize)
})

const endIndex = computed(() => {
  const index = startIndex.value + visibleCount.value + props.bufferSize * 2
  return Math.min(props.items.length - 1, index)
})

const visibleItems = computed(() => {
  const items = []
  for (let i = startIndex.value; i <= endIndex.value; i++) {
    if (props.items[i]) {
      items.push({
        data: props.items[i],
        index: i
      })
    }
  }
  return items
})

const offsetY = computed(() => startIndex.value * props.itemHeight)

// 滚动处理 - 使用节流优化性能
let scrollTimer: number | null = null
const handleScroll = (event: Event) => {
  const target = event.target as HTMLElement
  scrollTop.value = target.scrollTop

  // 发出滚动事件，但进行节流处理
  if (scrollTimer) {
    clearTimeout(scrollTimer)
  }
  scrollTimer = setTimeout(() => {
    emit('scroll', {
      scrollTop: scrollTop.value,
      visibleRange: { start: startIndex.value, end: endIndex.value }
    })
  }, 16) // 约60fps
}

// 定义事件
const emit = defineEmits<{
  scroll: [data: { scrollTop: number; visibleRange: { start: number; end: number } }]
}>()

// 滚动到指定项目
const scrollToItem = (index: number) => {
  if (!containerRef.value) return

  const targetScrollTop = index * props.itemHeight
  containerRef.value.scrollTop = targetScrollTop
  scrollTop.value = targetScrollTop
}

// 滚动到顶部
const scrollToTop = () => {
  scrollToItem(0)
}

// 滚动到底部
const scrollToBottom = () => {
  scrollToItem(props.items.length - 1)
}

// 监听数据变化，重置滚动位置
watch(
  () => props.items.length,
  (newLength, oldLength) => {
    // 如果数据长度发生变化，可能需要调整滚动位置
    if (newLength < oldLength && scrollTop.value > 0) {
      // 数据减少时，检查当前滚动位置是否超出范围
      const maxScrollTop = Math.max(0, totalHeight.value - props.containerHeight)
      if (scrollTop.value > maxScrollTop) {
        nextTick(() => {
          if (containerRef.value) {
            containerRef.value.scrollTop = maxScrollTop
            scrollTop.value = maxScrollTop
          }
        })
      }
    }
  }
)

// 性能监控
const performanceStats = ref({
  renderCount: 0,
  lastRenderTime: 0,
  averageRenderTime: 0
})

// 监控渲染性能
watch(
  visibleItems,
  () => {
    const renderTime = performance.now()
    performanceStats.value.renderCount++
    performanceStats.value.lastRenderTime = renderTime

    // 计算平均渲染时间（简单移动平均）
    if (performanceStats.value.renderCount === 1) {
      performanceStats.value.averageRenderTime = 0
    } else {
      const alpha = 0.1 // 平滑因子
      performanceStats.value.averageRenderTime =
        alpha * (renderTime - performanceStats.value.lastRenderTime) +
        (1 - alpha) * performanceStats.value.averageRenderTime
    }
  },
  { flush: 'post' }
)

// 暴露方法给父组件
defineExpose({
  scrollToItem,
  scrollToTop,
  scrollToBottom,
  getScrollTop: () => scrollTop.value,
  getVisibleRange: () => ({ start: startIndex.value, end: endIndex.value }),
  getPerformanceStats: () => performanceStats.value,
  // 调试方法
  getDebugInfo: () => ({
    totalItems: props.items.length,
    visibleCount: visibleCount.value,
    startIndex: startIndex.value,
    endIndex: endIndex.value,
    offsetY: offsetY.value,
    scrollTop: scrollTop.value,
    totalHeight: totalHeight.value,
    containerHeight: props.containerHeight,
    itemHeight: props.itemHeight,
    bufferSize: props.bufferSize
  })
})
</script>

<style scoped>
.virtual-list-container {
  overflow-y: auto;
  overflow-x: hidden;
}

.virtual-list-item {
  box-sizing: border-box;
}

/* 自定义滚动条样式 */
.virtual-list-container::-webkit-scrollbar {
  width: 8px;
}

.virtual-list-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.virtual-list-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.virtual-list-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
