/**
 * 加密工具类
 * 使用 Web Crypto API 实现 AES-GCM 加密
 */

// 加密配置
const CRYPTO_CONFIG = {
  algorithm: 'AES-GCM',
  keyLength: 256, // 256位密钥
  ivLength: 12,   // 96位初始化向量
  tagLength: 128  // 128位认证标签
}

// 加密结果接口
export interface EncryptedData {
  data: string      // Base64编码的加密数据
  iv: string        // Base64编码的初始化向量
  tag: string       // Base64编码的认证标签
}

/**
 * 生成加密密钥
 * 基于用户ID和固定盐值生成确定性密钥
 */
async function generateKey(userId: string): Promise<CryptoKey> {
  // 使用用户ID和固定盐值生成密钥材料
  const keyMaterial = await crypto.subtle.importKey(
    'raw',
    new TextEncoder().encode(`${userId}_message_encryption_key_2024`),
    'PBKDF2',
    false,
    ['deriveKey']
  )

  // 派生AES密钥
  return crypto.subtle.deriveKey(
    {
      name: 'PBKDF2',
      salt: new TextEncoder().encode('electron_im_salt_2024'),
      iterations: 100000,
      hash: 'SHA-256'
    },
    keyMaterial,
    {
      name: CRYPTO_CONFIG.algorithm,
      length: CRYPTO_CONFIG.keyLength
    },
    false,
    ['encrypt', 'decrypt']
  )
}

/**
 * 加密文本数据
 */
export async function encryptMessage(message: string, userId: string): Promise<EncryptedData> {
  try {
    // 生成密钥
    const key = await generateKey(userId)
    
    // 生成随机初始化向量
    const iv = crypto.getRandomValues(new Uint8Array(CRYPTO_CONFIG.ivLength))
    
    // 加密数据
    const encodedMessage = new TextEncoder().encode(message)
    const encryptedBuffer = await crypto.subtle.encrypt(
      {
        name: CRYPTO_CONFIG.algorithm,
        iv: iv,
        tagLength: CRYPTO_CONFIG.tagLength
      },
      key,
      encodedMessage
    )

    // 分离加密数据和认证标签
    const encryptedArray = new Uint8Array(encryptedBuffer)
    const tagLength = CRYPTO_CONFIG.tagLength / 8 // 转换为字节
    const data = encryptedArray.slice(0, -tagLength)
    const tag = encryptedArray.slice(-tagLength)

    return {
      data: arrayBufferToBase64(data),
      iv: arrayBufferToBase64(iv),
      tag: arrayBufferToBase64(tag)
    }
  } catch (error) {
    console.error('加密失败:', error)
    throw new Error(`消息加密失败: ${error.message}`)
  }
}

/**
 * 解密文本数据
 */
export async function decryptMessage(encryptedData: EncryptedData, userId: string): Promise<string> {
  try {
    // 生成密钥
    const key = await generateKey(userId)
    
    // 解码Base64数据
    const data = base64ToArrayBuffer(encryptedData.data)
    const iv = base64ToArrayBuffer(encryptedData.iv)
    const tag = base64ToArrayBuffer(encryptedData.tag)
    
    // 合并加密数据和认证标签
    const encryptedBuffer = new Uint8Array(data.byteLength + tag.byteLength)
    encryptedBuffer.set(new Uint8Array(data))
    encryptedBuffer.set(new Uint8Array(tag), data.byteLength)
    
    // 解密数据
    const decryptedBuffer = await crypto.subtle.decrypt(
      {
        name: CRYPTO_CONFIG.algorithm,
        iv: new Uint8Array(iv),
        tagLength: CRYPTO_CONFIG.tagLength
      },
      key,
      encryptedBuffer
    )
    
    return new TextDecoder().decode(decryptedBuffer)
  } catch (error) {
    console.error('解密失败:', error)
    throw new Error(`消息解密失败: ${error.message}`)
  }
}

/**
 * 加密对象数据
 */
export async function encryptObject<T>(obj: T, userId: string): Promise<EncryptedData> {
  const jsonString = JSON.stringify(obj)
  return encryptMessage(jsonString, userId)
}

/**
 * 解密对象数据
 */
export async function decryptObject<T>(encryptedData: EncryptedData, userId: string): Promise<T> {
  const jsonString = await decryptMessage(encryptedData, userId)
  return JSON.parse(jsonString)
}

/**
 * ArrayBuffer 转 Base64
 */
function arrayBufferToBase64(buffer: ArrayBuffer | Uint8Array): string {
  const bytes = buffer instanceof ArrayBuffer ? new Uint8Array(buffer) : buffer
  let binary = ''
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i])
  }
  return btoa(binary)
}

/**
 * Base64 转 ArrayBuffer
 */
function base64ToArrayBuffer(base64: string): ArrayBuffer {
  const binary = atob(base64)
  const bytes = new Uint8Array(binary.length)
  for (let i = 0; i < binary.length; i++) {
    bytes[i] = binary.charCodeAt(i)
  }
  return bytes.buffer
}

/**
 * 验证加密功能是否可用
 */
export async function testCrypto(): Promise<boolean> {
  try {
    const testMessage = 'Hello, World!'
    const testUserId = 'test_user'
    
    const encrypted = await encryptMessage(testMessage, testUserId)
    const decrypted = await decryptMessage(encrypted, testUserId)
    
    return decrypted === testMessage
  } catch (error) {
    console.error('加密功能测试失败:', error)
    return false
  }
}
